import { ref, reactive, watch, computed } from 'vue';
import { getServerList, getServerDetail as getServerDetailApi } from '@/api/serverList';
import {
	getLoadData as getLoadDataApi,
	getCpuAndMemoryData as getCpuAndMemoryDataApi,
	getNetworkIoData as getNetworkIoDataApi,
} from '@/api/control';
import { useConfigStore } from '@/store/modules/config';
import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import { getChartColorStops } from '@/pages/index/serverList/useController';
import { $t } from '@/locale/index.js';
import { getByteUnit } from '@/utils/common.js';
import bgLight from '@/static/index/bg-light.png';

// ==================== 网络数据状态 ====================
// 网络流量数据
export const upFlow = ref('0');
export const downFlow = ref('0');
export const upTotal = ref('0');
export const downTotal = ref('0');
export const networkData = reactive({
	xData: [], // 时间
	upData: [], // 上行数据
	downData: [], // 下行数据
});

// 磁盘IO数据
export const readBytes = ref('0');
export const writeBytes = ref('0');
export const ioCount = ref(0);
export const ioDelay = ref(0);
export const ioDelayColor = ref('#20a53a');
export const diskIOData = reactive({
	xData: [], // 时间
	readData: [], // 读取数据
	writeData: [], // 写入数据
});

// 图表数据 - 适配ECharts组件格式
export const networkChartData = reactive({
	categories: [], // x轴数据
	series: [
		{
			name: $t('linux.network.up'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
		{
			name: $t('linux.network.down'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
	],
	color: ['#ff8c00', '#1e90ff'],
});

// 磁盘IO图表数据 - 适配ECharts组件格式
export const diskIOChartData = reactive({
	categories: [], // x轴数据
	series: [
		{
			name: $t('linux.io.read'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
		{
			name: $t('linux.io.write'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
	],
	color: ['#FF4683', '#6CC0CF'],
});

// 定时器ID
let timer = null;

// ==================== 网络相关方法 ====================
export const networkInfo = ref({});

// 版本比较函数
export const compareVersion = (version1, version2) => {
	if (!version1 || !version2) return false;

	const v1Parts = version1.split('.').map(Number);
	const v2Parts = version2.split('.').map(Number);

	// 补齐版本号位数
	const maxLength = Math.max(v1Parts.length, v2Parts.length);
	while (v1Parts.length < maxLength) v1Parts.push(0);
	while (v2Parts.length < maxLength) v2Parts.push(0);

	// 逐位比较
	for (let i = 0; i < maxLength; i++) {
		if (v1Parts[i] > v2Parts[i]) return 1;
		if (v1Parts[i] < v2Parts[i]) return -1;
	}
	return 0;
};

// 检查是否支持节点管理功能
export const isNodeManagementSupported = ref(false);

// 页面容器引用
export const pageContainer = ref(null);

// ==================== 服务器信息显示相关 ====================
// 服务器基础信息
export const serverDisplayInfo = computed(() => {
	const { currentServerInfo } = useConfigStore().getReactiveState();

	if (!networkInfo.value) {
		return {
			name: currentServerInfo.value?.name || '宝塔面板',
			ip: currentServerInfo.value?.ip || '--',
			system: '--',
			uptime: '--',
			isOnline: false,
		};
	}

	return {
		name: networkInfo.value.title || currentServerInfo.value?.name || '宝塔面板',
		ip: currentServerInfo.value?.ip || networkInfo.value.ip || '--',
		system: networkInfo.value.simple_system || networkInfo.value.system || '--',
		uptime: `持续运行${networkInfo.value.time}` || '--',
		isOnline: networkInfo.value.status !== false,
	};
});

// 服务器状态指示器颜色
export const serverStatusColor = computed(() => {
	if (!networkInfo.value || networkInfo.value.status === false) {
		return '#E7E7E7'; // 离线状态为灰色
	}
	return '#20a50a'; // 在线状态为绿色
});

export const getNetwork = async () => {
	const { panelVersion } = useConfigStore().getReactiveState();
	try {
		const res = await getServerList();
		networkInfo.value = res;
		panelVersion.value = res.version;

		// 检查版本是否支持节点管理功能（需要 >= 9.7.0）
		isNodeManagementSupported.value = compareVersion(res.version, '9.7.0') >= 0;

		await getServerDetail(res);
		await getServerStatistics(res);
	} catch (error) {
		console.log(error);
	}
};

// 获取服务器统计
export const websiteTotal = ref(0);
export const safetyTotal = ref(0);
export const databaseTotal = ref(0);
export const getServerStatistics = async (info) => {
	try {
		websiteTotal.value = info.site_total;
		databaseTotal.value = info.database_total;
		safetyTotal.value = await getServerDetailApi();
	} catch (error) {}
};

// 服务器磁盘列表
export const diskList = ref([]);
// 负载数据状态
export const loadData = ref({
	one: 0, // 1分钟平均负载
	five: 0, // 5分钟平均负载
	fifteen: 0, // 15分钟平均负载
	max: 0, // 最大负载值
	limit: 0, // 负载限制值
	safe: 0, // 安全负载阈值
	percentage: 0, // 负载百分比
});

// CPU 数据状态（来自 getCpuAndMemoryData API）
export const cpuData = ref({
	current: 0, // 当前 CPU 使用率
	average: 0, // 平均 CPU 使用率
	max: 0, // 最大 CPU 使用率
	percentage: 0, // CPU 使用率百分比（用于进度条）
});

// 监控状态检测
export const isMonitoringEnabled = ref(false); // 监控是否已开启，初始为false，等待检测结果
export const isLoadingMonitorStatus = ref(false); // 是否正在检测监控状态
export const monitorStatusError = ref(null); // 监控状态检测错误信息

// ==================== 网络IO数据状态 ====================
// 网络IO实时数据（直接来自 networkInfo，与 serverList 页面保持一致）
export const networkIoData = computed(() => {
	if (!networkInfo.value) {
		return { up: 0, down: 0 };
	}
	return {
		up: parseFloat(networkInfo.value.up) || 0,
		down: parseFloat(networkInfo.value.down) || 0,
	};
});

// ==================== CPU 详细信息状态管理 ====================
// CPU 详细信息
export const cpuDetailInfo = ref({
	usage: 0, // CPU 使用率
	idle: 0, // CPU 空闲率
	logicalCores: 0, // 逻辑核心数
	physicalCores: 0, // 物理核心数
	physicalCpuCount: 0, // 物理 CPU 数量
	modelName: '', // CPU 型号名称
	coreUsages: [], // 各核心使用率数组
	isDataValid: false, // 数据是否有效
});

// ==================== 内存详细信息状态管理 ====================
// 内存详细信息
export const memDetailInfo = ref({
	memTotal: 0, // 总内存 (MB)
	memFree: 0, // 空闲内存 (MB)
	memBuffers: 0, // 缓冲区内存 (MB)
	memCached: 0, // 缓存内存 (MB)
	memAvailable: 0, // 可用内存 (MB)
	memShared: 0, // 共享内存 (MB)
	memRealUsed: 0, // 实际使用内存 (MB)
	memUsagePercentage: 0, // 内存使用率百分比
	memNewTotal: '', // 格式化的总内存
	memNewRealUsed: '', // 格式化的实际使用内存
	isDataValid: false, // 数据是否有效
});

// 内存数据状态（来自 getCpuAndMemoryData API）
export const memoryData = ref({
	current: 0, // 当前内存使用率
	average: 0, // 平均内存使用率
	max: 0, // 最大内存使用率
	percentage: 0, // 内存使用率百分比（用于进度条）
});

// 跳转到监控页面
export const navigateToMonitorPage = () => {
	try {
		uni.navigateTo({
			url: '/linux/control/index',
			animationType: 'zoom-fade-out',
		});
	} catch (error) {
		console.error('跳转到监控页面失败:', error);
		pageContainer.value?.notify?.error('跳转失败，请重试');
	}
};

// ==================== CPU 数据解析函数 ====================
/**
 * 解析 CPU 数据数组
 * @param {Array} cpuData - 接口返回的 CPU 数据数组
 * 数据格式：[3.7, 4, [2.6, 2.5, 2.5, 2.5], "AMD EPYC 7B12 * 1", 2, 1]
 * 索引说明：
 * - 0: CPU 使用率 (3.7%)
 * - 1: 逻辑核心数 (4)
 * - 2: 各核心使用率数组 ([2.6, 2.5, 2.5, 2.5] %)
 * - 3: CPU 型号名称 ("AMD EPYC 7B12 * 1")
 * - 4: 物理核心数 (2)
 * - 5: 物理 CPU 数量 (1)
 * @returns {Object} 解析后的 CPU 信息对象
 */
export const parseCpuData = (cpuData) => {
	try {
		// 验证输入数据
		if (!Array.isArray(cpuData) || cpuData.length < 6) {
			console.warn('CPU 数据格式不正确，使用默认值');
			return {
				usage: 0,
				idle: 100,
				logicalCores: 0,
				physicalCores: 0,
				physicalCpuCount: 0,
				modelName: '未知',
				coreUsages: [],
				isDataValid: false,
			};
		}

		// 解析各项数据
		const usage = parseFloat(cpuData[0]) || 0;
		const idle = Math.max(0, Math.min(100, 100 - usage)); // 计算空闲率，确保在 0-100 范围内
		const logicalCores = parseInt(cpuData[1]) || 0;
		const coreUsages = Array.isArray(cpuData[2]) ? cpuData[2].map((usage) => parseFloat(usage) || 0) : [];
		const modelName = typeof cpuData[3] === 'string' ? cpuData[3].trim() : '未知';
		const physicalCores = parseInt(cpuData[4]) || 0;
		const physicalCpuCount = parseInt(cpuData[5]) || 0;

		// 验证关键数据的合理性
		const isDataValid = usage >= 0 && usage <= 100 && logicalCores > 0 && physicalCores > 0;

		return {
			usage: Math.round(usage * 100) / 100, // 保留两位小数
			idle: Math.round(idle * 100) / 100, // 保留两位小数
			logicalCores,
			physicalCores,
			physicalCpuCount,
			modelName,
			coreUsages,
			isDataValid,
		};
	} catch (error) {
		console.error('解析 CPU 数据时发生错误:', error);
		return {
			usage: 0,
			idle: 100,
			logicalCores: 0,
			physicalCores: 0,
			physicalCpuCount: 0,
			modelName: '解析失败',
			coreUsages: [],
			isDataValid: false,
		};
	}
};

// ==================== 内存数据解析函数 ====================
/**
 * 解析内存数据对象
 * @param {Object} memData - 接口返回的内存数据对象
 * 数据格式：{
 *   memTotal: 957,           // 总内存 (MB)
 *   memFree: 91,            // 空闲内存 (MB)
 *   memBuffers: 20,         // 缓冲区内存 (MB)
 *   memCached: 425,         // 缓存内存 (MB)
 *   memAvailable: 356,      // 可用内存 (MB)
 *   memShared: 0,           // 共享内存 (MB)
 *   memRealUsed: 421,       // 实际使用内存 (MB)
 *   memNewRealUsed: "421",  // 格式化的实际使用内存
 *   memNewRealUsedList: ["421.0", "MB"],  // 分离的数值和单位
 *   memNewTotal: "957.4MB", // 格式化的总内存
 *   memNewTotalList: ["957.4", "MB"]      // 分离的数值和单位
 * }
 * @returns {Object} 解析后的内存信息对象
 */
export const parseMemoryData = (memData) => {
	try {
		// 验证输入数据
		if (!memData || typeof memData !== 'object') {
			console.warn('内存数据格式不正确，使用默认值');
			return {
				memTotal: 0,
				memFree: 0,
				memBuffers: 0,
				memCached: 0,
				memAvailable: 0,
				memShared: 0,
				memRealUsed: 0,
				memUsagePercentage: 0,
				memNewTotal: '0MB',
				memNewRealUsed: '0MB',
				isDataValid: false,
			};
		}

		// 解析各项数据
		const memTotal = parseFloat(memData.memTotal) || 0;
		const memFree = parseFloat(memData.memFree) || 0;
		const memBuffers = parseFloat(memData.memBuffers) || 0;
		const memCached = parseFloat(memData.memCached) || 0;
		const memAvailable = parseFloat(memData.memAvailable) || 0;
		const memShared = parseFloat(memData.memShared) || 0;
		const memRealUsed = parseFloat(memData.memRealUsed) || 0;

		// 计算内存使用率百分比
		const memUsagePercentage = memTotal > 0 ? Math.round((memRealUsed / memTotal) * 1000) / 10 : 0;

		// 获取格式化的内存值
		const memNewTotal = memData.memNewTotal || `${memTotal}MB`;
		const memNewRealUsed = memData.memNewRealUsed || `${memRealUsed}MB`;

		// 验证关键数据的合理性
		const isDataValid = memTotal > 0 && memRealUsed >= 0 && memRealUsed <= memTotal;

		return {
			memTotal: Math.round(memTotal * 100) / 100, // 保留两位小数
			memFree: Math.round(memFree * 100) / 100,
			memBuffers: Math.round(memBuffers * 100) / 100,
			memCached: Math.round(memCached * 100) / 100,
			memAvailable: Math.round(memAvailable * 100) / 100,
			memShared: Math.round(memShared * 100) / 100,
			memRealUsed: Math.round(memRealUsed * 100) / 100,
			memUsagePercentage: Math.round(memUsagePercentage * 100) / 100,
			memNewTotal,
			memNewRealUsed,
			isDataValid,
		};
	} catch (error) {
		console.error('解析内存数据时发生错误:', error);
		return {
			memTotal: 0,
			memFree: 0,
			memBuffers: 0,
			memCached: 0,
			memAvailable: 0,
			memShared: 0,
			memRealUsed: 0,
			memUsagePercentage: 0,
			memNewTotal: '解析失败',
			memNewRealUsed: '解析失败',
			isDataValid: false,
		};
	}
};

// 格式化负载值显示
export const formatLoadValue = (value) => {
	if (typeof value !== 'number') {
		value = parseFloat(value) || 0;
	}
	return value.toFixed(2);
};

// 格式化 CPU 值显示
export const formatCpuValue = (value) => {
	if (typeof value !== 'number') {
		value = parseFloat(value) || 0;
	}
	return value.toFixed(1) + '%';
};

// 格式化内存值显示
export const formatMemoryValue = (value, unit = 'auto') => {
	if (typeof value !== 'number') {
		value = parseFloat(value) || 0;
	}

	// 如果指定了单位，直接返回
	if (unit !== 'auto') {
		return value.toFixed(1) + unit;
	}

	// 自动选择合适的单位
	if (value >= 1024) {
		// 转换为 GB
		return (value / 1024).toFixed(1) + 'GB';
	} else {
		// 保持 MB
		return value.toFixed(1) + 'MB';
	}
};

// 格式化内存百分比显示
export const formatMemoryPercentage = (value) => {
	if (typeof value !== 'number') {
		value = parseFloat(value) || 0;
	}
	return value.toFixed(1) + '%';
};

// 格式化网络速度显示（智能单位转换）
export const formatNetworkSpeed = (value) => {
	if (typeof value !== 'number') {
		value = parseFloat(value) || 0;
	}

	// 使用 getByteUnit 进行智能单位转换
	// value 是 KB/s，需要转换为字节再使用 getByteUnit
	const bytesPerSecond = value * 1024;
	const formattedValue = getByteUnit(bytesPerSecond, false, 1); // 不显示单位，保留1位小数
	const unit = getByteUnit(bytesPerSecond, true, 1).split(' ')[1]; // 获取单位

	return `${formattedValue} ${unit}/s`;
};

// 格式化网络数据显示（用于基础信息展示）
export const formatNetworkData = (upValue, downValue) => {
	return {
		up: formatNetworkSpeed(upValue),
		down: formatNetworkSpeed(downValue),
	};
};

// 更新负载图表数据
export const updateLoadChartData = async () => {
	try {
		isLoadingMonitorStatus.value = true;
		monitorStatusError.value = null;

		// 获取当前时间和24小时前的时间戳
		const endTime = Math.floor(Date.now() / 1000);
		const startTime = endTime - 24 * 60 * 60; // 24小时前

		// 调用API获取负载数据
		const response = await getLoadDataApi({
			start: startTime,
			end: endTime,
		});

		if (response && Array.isArray(response)) {
			if (response.length === 0) {
				// 检测到空数组，说明监控未开启
				isMonitoringEnabled.value = false;
				loadChartData.series[0].data = [];
			} else {
				// 有数据，说明监控已开启
				isMonitoringEnabled.value = true;
				// 计算负载使用率
				const loadUsageData = response.map((item) => parseFloat(item.pro) || 0);
				// 直接更新 loadChartData，createChartData 已经处理好一切
				loadChartData.series[0].data = loadUsageData;
			}
		} else {
			// 响应格式异常
			isMonitoringEnabled.value = false;
			loadChartData.series[0].data = [];
		}
	} catch (error) {
		console.error('获取负载数据失败:', error);
		monitorStatusError.value = error.message || '获取监控数据失败';
		isMonitoringEnabled.value = false;
		// 使用默认数据
		loadChartData.series[0].data = [0, 0, 0, 0, 0, 0];
	} finally {
		isLoadingMonitorStatus.value = false;
	}
};

// 更新 CPU 和内存图表数据
export const updateCpuChartData = async () => {
	try {
		isLoadingMonitorStatus.value = true;
		monitorStatusError.value = null;

		// 获取当前时间和24小时前的时间戳
		const endTime = Math.floor(Date.now() / 1000);
		const startTime = endTime - 24 * 60 * 60; // 24小时前

		// 调用API获取CPU和内存数据
		const response = await getCpuAndMemoryDataApi({
			start: startTime,
			end: endTime,
		});

		if (response && Array.isArray(response)) {
			if (response.length === 0) {
				// 检测到空数组，说明监控未开启
				isMonitoringEnabled.value = false;
				cpuChartData.series[0].data = [];
				memoryChartData.series[0].data = [];
				// 重置 CPU 数据状态
				cpuData.value = {
					current: 0,
					average: 0,
					max: 0,
					percentage: 0,
				};
				// 重置内存数据状态
				memoryData.value = {
					current: 0,
					average: 0,
					max: 0,
					percentage: 0,
				};
			} else {
				// 有数据，说明监控已开启
				isMonitoringEnabled.value = true;

				// 提取 CPU 使用率数据（pro 字段）
				const cpuUsageData = response.map((item) => parseFloat(item.pro) || 0);
				// 更新 cpuChartData
				cpuChartData.series[0].data = cpuUsageData;

				// 计算 CPU 统计数据
				const cpuCurrent = cpuUsageData[cpuUsageData.length - 1] || 0; // 最新值
				const cpuAverage = cpuUsageData.reduce((sum, val) => sum + val, 0) / cpuUsageData.length || 0; // 平均值
				const cpuMax = Math.max(...cpuUsageData) || 0; // 最大值

				// 更新 CPU 数据状态
				cpuData.value = {
					current: Math.round(cpuCurrent * 100) / 100,
					average: Math.round(cpuAverage * 100) / 100,
					max: Math.round(cpuMax * 100) / 100,
					percentage: Math.min(100, Math.max(0, cpuCurrent)), // 确保在 0-100 范围内
				};

				// 提取内存使用率数据（mem 字段）
				const memoryUsageData = response.map((item) => parseFloat(item.mem) || 0);
				// 更新 memoryChartData
				memoryChartData.series[0].data = memoryUsageData;

				// 计算内存统计数据
				const memoryCurrent = memoryUsageData[memoryUsageData.length - 1] || 0; // 最新值
				const memoryAverage = memoryUsageData.reduce((sum, val) => sum + val, 0) / memoryUsageData.length || 0; // 平均值
				const memoryMax = Math.max(...memoryUsageData) || 0; // 最大值

				// 更新内存数据状态
				memoryData.value = {
					current: Math.round(memoryCurrent * 100) / 100,
					average: Math.round(memoryAverage * 100) / 100,
					max: Math.round(memoryMax * 100) / 100,
					percentage: Math.min(100, Math.max(0, memoryCurrent)), // 确保在 0-100 范围内
				};
			}
		} else {
			// 响应格式异常
			isMonitoringEnabled.value = false;
			cpuChartData.series[0].data = [];
			memoryChartData.series[0].data = [];
			// 重置 CPU 数据状态
			cpuData.value = {
				current: 0,
				average: 0,
				max: 0,
				percentage: 0,
			};
			// 重置内存数据状态
			memoryData.value = {
				current: 0,
				average: 0,
				max: 0,
				percentage: 0,
			};
		}
	} catch (error) {
		console.error('获取CPU和内存数据失败:', error);
		monitorStatusError.value = error.message || '获取CPU和内存监控数据失败';
		isMonitoringEnabled.value = false;
		// 使用默认数据
		cpuChartData.series[0].data = [0, 0, 0, 0, 0, 0];
		memoryChartData.series[0].data = [0, 0, 0, 0, 0, 0];
		// 重置数据状态
		cpuData.value = {
			current: 0,
			average: 0,
			max: 0,
			percentage: 0,
		};
		memoryData.value = {
			current: 0,
			average: 0,
			max: 0,
			percentage: 0,
		};
	} finally {
		isLoadingMonitorStatus.value = false;
	}
};

// 更新网络IO详情数据（仅在详情页调用）
export const updateNetworkIoData = async () => {
	try {
		// 获取当前时间和24小时前的时间戳
		const endTime = Math.floor(Date.now() / 1000);
		const startTime = endTime - 24 * 60 * 60; // 24小时前

		// 调用API获取网络IO数据
		const response = await getNetworkIoDataApi({
			start: startTime,
			end: endTime,
		});

		if (response && Array.isArray(response)) {
			if (response.length === 0) {
				// 检测到空数组，重置图表数据
				newNetworkChartData.series[0].data = [];
				newNetworkChartData.series[1].data = [];
			} else {
				// 有数据，更新网络IO图表
				// 提取上传和下载数据
				const upData = response.map((item) => parseFloat(item.up) || 0);
				const downData = response.map((item) => parseFloat(item.down) || 0);

				// 更新图表数据
				newNetworkChartData.series[0].data = upData;
				newNetworkChartData.series[1].data = downData;
			}
		} else {
			// 响应格式异常，重置图表数据
			newNetworkChartData.series[0].data = [];
			newNetworkChartData.series[1].data = [];
		}
	} catch (error) {
		console.error('获取网络IO详情数据失败:', error);
		// 发生错误时重置图表数据
		newNetworkChartData.series[0].data = [0, 0, 0, 0, 0, 0];
		newNetworkChartData.series[1].data = [0, 0, 0, 0, 0, 0];
	}
};

// 处理网络详情展开/收起事件
export const handleNetworkToggle = (isExpanded) => {
	if (isExpanded && isMonitoringEnabled.value) {
		// 只有在展开且监控已开启时才加载详情数据
		updateNetworkIoData();
	}
};

// 获取服务器详情的值
export const chartMap = ref({});
export const getServerDetail = async (info) => {
	try {
		// 处理负载数据
		if (info.load) {
			loadData.value.one = info.load.one || 0;
			loadData.value.five = info.load.five || 0;
			loadData.value.fifteen = info.load.fifteen || 0;
			loadData.value.max = info.load.max || 0;
			loadData.value.limit = info.load.limit || 0;
			loadData.value.safe = info.load.safe || 0;

			// 计算负载百分比（基于1分钟负载和最大值）
			let loadCount =
				Math.round((info.load.one / info.load.max) * 100) > 100
					? 100
					: Math.round((info.load.one / info.load.max) * 100);
			loadCount = loadCount < 0 ? 0 : loadCount;
			loadData.value.percentage = loadCount;

			chartMap.value['load'] = handleServerInfo(loadCount, 'load');
		}

		// CPU 数据处理
		if (info.cpu && Array.isArray(info.cpu)) {
			// 解析 CPU 详细信息
			const parsedCpuData = parseCpuData(info.cpu);
			cpuDetailInfo.value = parsedCpuData;

			// 保持原有的 CPU 使用率处理逻辑（向后兼容）
			let cpuCount = info.cpu[0] || 0;
			chartMap.value['cpu'] = handleServerInfo(cpuCount, 'cpu');
		} else {
			// 如果 CPU 数据格式不正确，使用默认值
			console.warn('CPU 数据格式异常，使用默认值');
			cpuDetailInfo.value = parseCpuData([]);
			chartMap.value['cpu'] = handleServerInfo(0, 'cpu');
		}
		// 内存数据处理
		if (info.mem && typeof info.mem === 'object') {
			// 解析内存详细信息
			const parsedMemoryData = parseMemoryData(info.mem);
			memDetailInfo.value = parsedMemoryData;

			// 保持原有的内存使用率处理逻辑（向后兼容）
			const memCount = Math.round((info.mem.memRealUsed / info.mem.memTotal) * 1000) / 10; // 返回 memRealUsed 占 memTotal 的百分比
			chartMap.value['mem'] = handleServerInfo(memCount, 'mem');
		} else {
			// 如果内存数据格式不正确，使用默认值
			console.warn('内存数据格式异常，使用默认值');
			memDetailInfo.value = parseMemoryData({});
			chartMap.value['mem'] = handleServerInfo(0, 'mem');
		}
		diskList.value = info.disk;
		let diskJson = [];
		for (let i = 0; i < diskList.value.length; i++) {
			let ratio = diskList.value[i].size[3];
			ratio = parseFloat(ratio.substring(0, ratio.lastIndexOf('%')));
			let diskInfo = handleDiskInfo(ratio, i);
			diskJson.push(diskInfo);
		}
		chartMap.value['disk'] = diskJson;
		updateChartData();
		handlePicker(info);
	} catch (error) {
		console.log(error);
	}
};

// 处理磁盘信息
export const handleDiskInfo = (ratio, index) => {
	try {
		let diskInfo = {};
		// 记录实际负载值
		diskInfo.val = ratio;

		diskInfo.path = networkInfo.value.disk[index].path;

		diskInfo.title = networkInfo.value.disk[index].size[0];

		return diskInfo;
	} catch (error) {}
};
// 处理对应的服务器信息
export const handleServerInfo = (number, name) => {
	try {
		// 根据负载区间判断状态
		const loadList = [
			{ val: 90, title: $t('linux.blocked'), color: '#dd2f00' },
			{ val: 80, title: $t('linux.slow'), color: '#ff9900' },
			{ val: 70, title: $t('linux.normal'), color: '#20a53a' },
			{ val: 30, title: $t('linux.smooth'), color: '#20a53a' },
		];

		let activeInfo = {};

		// 从高到低匹配第一个符合条件的负载区间
		for (let i = 0; i < loadList.length; i++) {
			if (number >= loadList[i].val) {
				activeInfo = { ...loadList[i] };
				break;
			} else if (number <= 30) {
				activeInfo = { ...loadList[3] };
				break;
			}
		}

		// 记录实际负载值
		activeInfo.val = number;

		switch (name) {
			case 'load':
				activeInfo.title = activeInfo.title;
				break;
			case 'cpu':
				activeInfo.title = `${networkInfo.value.cpu[1]} ${$t('linux.cores')}`;
				break;
			case 'mem':
				activeInfo.title = `${Math.round(networkInfo.value.mem.memTotal / 1024)} G`;
				break;
		}

		return activeInfo;
	} catch (error) {
		console.error('处理服务器信息出错:', error);
		return null;
	}
};

// 根据IO延迟设置颜色
export const updateIODelayColor = (delay) => {
	if (delay > 100 && delay < 1000) {
		ioDelayColor.value = '#ff9900';
	} else if (delay >= 1000) {
		ioDelayColor.value = '#dd2f00';
	} else {
		ioDelayColor.value = '#20a53a';
	}
};

// 格式化字节大小
export const formatBytes = (bytes) => {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取当前时间格式化字符串
export const getCurrentTime = () => {
	const now = new Date();
	const hours = now.getHours().toString().padStart(2, '0');
	const minutes = now.getMinutes().toString().padStart(2, '0');
	const seconds = now.getSeconds().toString().padStart(2, '0');
	return `${hours}:${minutes}:${seconds}`;
};

// 更新图表数据
export const updateChartData = () => {
	// 模拟获取网络和磁盘IO数据
	const currentTime = getCurrentTime();
	if (networkValue.value === 'ALL') {
		upFlow.value = networkInfo.value.up;
		downFlow.value = networkInfo.value.down;
	} else {
		upFlow.value = networkInfo.value.network[networkValue.value].up;
		downFlow.value = networkInfo.value.network[networkValue.value].down;
	}

	// 添加新数据点，并限制为5个数据点
	if (networkData.xData.length >= 5) {
		networkData.xData.shift();
		networkData.upData.shift();
		networkData.downData.shift();
	}
	networkData.xData.push(currentTime);
	networkData.upData.push(upFlow.value);
	networkData.downData.push(downFlow.value);

	// 更新网络图表数据 - 使用深拷贝触发视图更新
	networkChartData.categories = [...networkData.xData];
	networkChartData.series[0].data = [...networkData.upData];
	networkChartData.series[1].data = [...networkData.downData];

	// 磁盘IO数据
	let ioData = networkInfo.value.iostat[ioValue.value];
	readBytes.value = ioData.read_bytes;
	writeBytes.value = ioData.write_bytes;

	// ioCount.value = Math.floor(Math.random() * 100);
	// ioDelay.value = Math.floor(Math.random() * 200);
	// updateIODelayColor(ioDelay.value);

	// 添加新数据点，并限制为5个数据点
	if (diskIOData.xData.length >= 5) {
		diskIOData.xData.shift();
		diskIOData.readData.shift();
		diskIOData.writeData.shift();
	}
	diskIOData.xData.push(currentTime);
	diskIOData.readData.push(readBytes.value / 1024 / 1024).toFixed(2);
	diskIOData.writeData.push(writeBytes.value / 1024 / 1024).toFixed(2);

	// 更新磁盘IO图表数据 - 使用深拷贝触发视图更新
	diskIOChartData.categories = [...diskIOData.xData];
	diskIOChartData.series[0].data = [...diskIOData.readData];
	diskIOChartData.series[1].data = [...diskIOData.writeData];

	// 强制更新图表配置，确保x轴数据刷新
	chartOpts.xAxis.data = [...networkChartData.categories];
};

// 根据类型初始化图表数据格式
export const initChartByType = (type) => {
	if (type === 0) {
		// 网络图表
		networkChartData.series.forEach((item) => {
			item.type = 'line';
		});
	} else {
		// 磁盘IO图表
		diskIOChartData.series.forEach((item) => {
			item.type = 'line';
		});
	}
};

// 生成初始数据
export const initData = () => {
	// 重置数据数组，确保每次调用都是全新的数据
	networkData.xData = [];
	networkData.upData = [];
	networkData.downData = [];
	diskIOData.xData = [];
	diskIOData.readData = [];
	diskIOData.writeData = [];
	networkList.value = [];
	ioList.value = [];
	networkValue.value = 'ALL';
	ioValue.value = 'ALL';
	networkName.value = $t('linux.all');
	ioName.value = $t('linux.all');
	subsecType.value = 0;

	// 重置监控状态
	isMonitoringEnabled.value = false;
	isLoadingMonitorStatus.value = false;
	monitorStatusError.value = null;

	// 初始化负载图表数据
	updateLoadChartData();

	// 初始化 CPU 图表数据
	updateCpuChartData();
};

// 启动定时器
export const startTimer = () => {
	// 确保先停止任何可能存在的定时器
	stopTimer();

	// 设置定时器，每3秒更新一次数据
	// 负载基础信息和MetricProgressBar会通过getNetwork->getServerDetail自动更新
	timer = setInterval(() => {
		getNetwork();
		// 每次更新时也更新 CPU 图表数据
		updateCpuChartData();
	}, 3000);
};

// 停止定时器
export const stopTimer = () => {
	// 清除定时器
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
};

// ==================== 图表选项 ====================
// 图表基础配置
export const chartOpts = reactive({
	grid: {
		bottom: 30,
		right: 20,
	},
	xAxis: {
		axisLine: {},
		axisLabel: {
			fontSize: 10,
			margin: 10,
			rotate: 0,
		},
		type: 'category',
		data: [],
	},
	yAxis: {
		scale: true, // 启用y轴自动缩放
		nameTextStyle: {
			padding: [0, 0, 0, 0],
			color: '#999999',
			fontSize: 10,
		},
		axisLabel: {
			color: '#999999',
			fontSize: 10,
			margin: 10,
		},
		splitLine: {
			lineStyle: {
				color: '#999999',
				type: 'dashed', // 设置为虚线类型
			},
		},
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'cross',
		},
	},
	legend: {
		top: 5,
		left: 'center',
		textStyle: {
			color: '#999999',
			fontSize: 10,
		},
		itemWidth: 15,
		itemHeight: 10,
		itemGap: 20,
	},
	title: {
		left: 'center',
		top: 10,
		textStyle: {
			fontSize: 10,
		},
	},
});

// 网络图表专属配置
export const getNetworkChartOpts = () => {
	return {
		...chartOpts,
		xAxis: {
			...chartOpts.xAxis,
			data: networkChartData.categories,
		},
		yAxis: {
			...chartOpts.yAxis,
			name: $t('linux.unitKBS'),
			axisLabel: {
				...chartOpts.yAxis.axisLabel,
				formatter: (value) => `${value} KB/s`,
			},
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
	};
};

// IO图表专属配置
export const getDiskIOChartOpts = () => {
	return {
		...chartOpts,
		xAxis: {
			...chartOpts.xAxis,
			data: diskIOChartData.categories,
		},
		yAxis: {
			...chartOpts.yAxis,
			name: $t('linux.unitMBS'),
			axisLabel: {
				...chartOpts.yAxis.axisLabel,
				formatter: (value) => `${value} MB/s`,
			},
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
	};
};

// ==================== 区域控制 ====================
export const picker = ref(null);
export const subsecType = ref(0); // 0: 网络, 1: 磁盘IO
export const networkList = ref([]);
export const networkName = ref($t('linux.all'));
export const networkValue = ref('ALL');
export const ioList = ref([]);
export const ioName = ref($t('linux.all'));
export const ioValue = ref('ALL');
export const defaultIndex = ref([0]);

export const openPicker = () => {
	let index = -1;

	if (subsecType.value === 0) {
		index = networkList.value.findIndex((item) => item.value === networkValue.value);
	} else {
		index = ioList.value.findIndex((item) => item.value === ioValue.value);
	}

	// 确保找到有效索引，否则默认为第一项
	defaultIndex.value = [index >= 0 ? index : 0];

	// 确保picker存在再调用open方法
	picker.value?.open();
};

const handlePicker = (res) => {
	// 网卡
	if (networkList.value.length == 0 && ioList.value.length == 0) {
		for (let key in res.iostat) {
			let ioItem = {
				title: key == 'ALL' ? $t('linux.all') : key,
				value: key,
			};
			ioList.value.push(ioItem);
		}

		networkList.value.push({
			title: $t('linux.all'),
			value: 'ALL',
		});

		for (let key in res.network) {
			let netItem = {
				title: key,
				value: key,
			};
			networkList.value.push(netItem);
		}
	}
};

export const confirm = (e) => {
	if (subsecType.value === 0) {
		networkValue.value = e.value[0].value;
		networkName.value = e.value[0].title;
	} else {
		ioValue.value = e.value[0].value;
		ioName.value = e.value[0].title;
	}
};

// 当subsection切换时
export const onSectionChange = (index) => {
	subsecType.value = index;
	if (index === 0) {
		networkValue.value = 'ALL';
		networkName.value = $t('linux.all');
	} else {
		ioValue.value = 'ALL';
		ioName.value = $t('linux.all');
	}
};

export const handleModuleAction = (type) => {
	switch (type) {
		case 'website':
			uni.navigateTo({
				url: '/linux/website/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'database':
			uni.navigateTo({
				url: '/linux/database/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'file':
			uni.navigateTo({
				url: '/linux/files/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'security':
			uni.navigateTo({
				url: '/linux/firewall/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'terminal':
			uni.navigateTo({
				url: '/pages/ssh/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'control':
			uni.navigateTo({
				url: '/linux/control/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'ssh':
			uni.navigateTo({
				url: '/linux/ssh/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'setting':
			uni.navigateTo({
				url: '/linux/setting/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'monitorReport':
			uni.navigateTo({
				url: '/linux/monitorReport/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'nginx':
			uni.navigateTo({
				url: '/linux/nginx/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'node':
			uni.navigateTo({
				url: '/linux/node/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'nginxEnv':
			uni.navigateTo({
				url: '/linux/nginxEnv/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'crontab':
			uni.navigateTo({
				url: '/linux/crontab/index',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'mysql':
			uni.navigateTo({
				url: '/linux/mysql/index',
				animationType: 'zoom-fade-out',
			});
			break;
	}
};

// ==================== 功能列表点击事件处理 ====================

/**
 * 处理基础功能列表项点击事件
 * @param {Object} event - 点击事件对象，包含 item 和 index
 */
export const handleBasicFunctionClick = (event) => {
	const { item } = event;
	const label = item.label;
	switch (label) {
		case '网站':
			handleModuleAction('website');
			break;
		case '数据库':
			handleModuleAction('database');
			break;
		case '系统防火墙':
			handleModuleAction('security');
			break;
		case '终端':
			handleModuleAction('terminal');
			break;
		case '安全风险':
			// TODO: 添加安全风险页面路由
			pageContainer.value?.notify?.primary('安全风险功能开发中...');
			break;
		case '监控':
			handleModuleAction('control');
			break;
		case 'SSH管理':
			handleModuleAction('ssh');
			break;
		case '文件管理':
			handleModuleAction('file');
			break;
		case '计划任务':
			handleModuleAction('crontab');
			break;
		case '日志':
			// TODO: 添加日志页面路由
			pageContainer.value?.notify?.primary('日志功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知功能类型');
	}
};

/**
 * 处理插件功能列表项点击事件
 * @param {Object} event - 点击事件对象，包含 item 和 index
 */
export const handlePluginFunctionClick = (event) => {
	const { item } = event;
	const label = item.label;

	switch (label) {
		case '站点监控':
			handleModuleAction('monitorReport');
			break;
		case '系统加固':
			// TODO: 添加系统加固页面路由
			pageContainer.value?.notify?.primary('系统加固功能开发中...');
			break;
		case 'nginx防火墙':
			handleModuleAction('nginx');
			break;
		case '防篡改':
			// TODO: 添加防篡改页面路由
			pageContainer.value?.notify?.primary('防篡改功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知插件类型');
	}
};

/**
 * 处理环境功能列表项点击事件
 * @param {Object} event - 点击事件对象，包含 item 和 index
 */
export const handleEnvironmentFunctionClick = (event) => {
	const { item } = event;
	const label = item.label;

	switch (label) {
		case 'nginx':
			handleModuleAction('nginxEnv');
			break;
		case 'mysql':
			handleModuleAction('mysql');
			break;
		case 'redis':
			// TODO: 添加 Redis 环境管理页面路由
			pageContainer.value?.notify?.primary('Redis 环境管理功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知环境类型');
	}
};

/**
 * 处理功能列表编辑按钮点击事件
 * @param {String} listType - 列表类型：'basic', 'plugin', 'environment'
 */
export const handleFunctionListEdit = (listType) => {
	switch (listType) {
		case 'basic':
			pageContainer.value?.notify?.primary('基础功能编辑功能开发中...');
			break;
		case 'plugin':
			pageContainer.value?.notify?.primary('插件功能编辑功能开发中...');
			break;
		case 'environment':
			pageContainer.value?.notify?.primary('环境功能编辑功能开发中...');
			break;
		default:
			pageContainer.value?.notify?.warning('未知编辑类型');
	}
};

// ==================== 监听数据变化 ====================
// 监听网络和磁盘IO数据变化
watch(
	() => ([...networkChartData.categories], [...diskIOChartData.categories]),
	() => {
		// 确保图表配置的xAxis数据也同步更新
		if (subsecType.value === 0) {
			// 网络图表当前激活
			chartOpts.xAxis.data = [...networkChartData.categories];
		} else {
			// 磁盘IO图表当前激活
			chartOpts.xAxis.data = [...diskIOChartData.categories];
		}
	},
	{ deep: true },
);

// 监听子系统类型变化，切换对应的x轴数据
watch(
	() => subsecType.value,
	(newType) => {
		if (newType === 0) {
			// 切换到网络图表
			chartOpts.xAxis.data = [...networkChartData.categories];
		} else {
			// 切换到磁盘IO图表
			chartOpts.xAxis.data = [...diskIOChartData.categories];
		}
	},
);

// 获取磁盘列表名称
export const getDiskListName = (path) => {
	if (path === '/') {
		return '/';
	}
	const parts = path.split('/').filter((part) => part.length > 0);
	if (parts.length > 0) {
		return '/' + parts[parts.length - 1];
	}
	// 如果路径无效或为空，也返回 '/'
	return '/';
};

// 获取基础图表配置
export const getBaseChartConfig = (usage, label) => {
	// 确保usage是数字
	usage = parseFloat(usage) || 0;

	// 创建颜色渐变配置
	const colorStops = getChartColorStops(usage);

	// 创建仪表盘数据
	const gaugeData = [
		{
			value: usage,
			name: label,
			title: {
				show: false,
			},
			detail: {
				valueAnimation: true,
				offsetCenter: [0, '0%'],
				fontSize: 10,
				color: '#A7A7A7',
				formatter: '{value}%',
			},
			itemStyle: {
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 0,
					y2: 1,
					colorStops: [...colorStops], // 使用深拷贝
				},
			},
		},
	];

	// 返回图表配置
	return {
		series: [
			{
				type: 'gauge',
				radius: '100%',
				center: ['65%', '65%'],
				startAngle: 180,
				endAngle: 0,
				pointer: {
					show: false,
				},
				progress: {
					show: true,
					overlap: false,
					roundCap: true,
					clip: false,
					itemStyle: {
						borderWidth: 6,
					},
				},
				axisLine: {
					lineStyle: {
						width: 8,
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					show: false,
					fontSize: 9,
				},
				title: {
					show: false,
				},
				data: gaugeData,
				detail: {
					valueAnimation: true,
					fontSize: 10,
					color: '#A7A7A7',
					formatter: '{value}%',
					borderRadius: 20,
				},
				animation: true,
				animationDuration: 1500,
				animationDurationUpdate: 1000,
				animationEasing: 'cubicInOut',
			},
		],
	};
};

// ==================== 图表配置重构 ====================
// 公共图表配置 - 定义通用的样式和行为
const commonChartConfig = {
	series: {
		type: 'line',
		smooth: true,
		symbol: 'none',
		lineStyle: {
			width: 2,
		},
		areaStyle: {
			color: {
				type: 'linear',
				x: 0,
				y: 0,
				x2: 0,
				y2: 1,
				colorStops: [
					{
						offset: 0,
						color: '', // 将在工厂函数中动态设置
					},
					{
						offset: 1,
						color: '', // 将在工厂函数中动态设置
					},
				],
			},
		},
	},
	// 默认数据
	defaultData: [820, 932, 901, 934, 1290, 1330, 1320, 1320, 1320, 1320, 1883, 0, 123, 456, 199],
};

// 图表颜色配置映射
const chartColorConfig = {
	load: {
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
	},
	cpu: {
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
	},
	memory: {
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
	},
	// 双面积图颜色配置
	network: {
		// 第一个系列（上传）
		primary: 'rgba(170, 255, 128, 1)',
		topGradient: 'rgba(170, 255, 128, 0.8)',
		bottomGradient: 'rgba(170, 255, 128, 0.1)',
		// 第二个系列（下载）
		secondary: 'rgba(255, 140, 0, 1)', // #1e90ff
		secondaryTopGradient: 'rgba(255, 140, 0, 0.8)',
		secondaryBottomGradient: 'rgba(255, 140, 0, 0.1)',
	},
	diskio: {
		// 第一个系列（读取）
		primary: 'rgba(255, 70, 131, 1)', // #FF4683
		topGradient: 'rgba(255, 70, 131, 0.8)',
		bottomGradient: 'rgba(255, 70, 131, 0.1)',
		// 第二个系列（写入）
		secondary: 'rgba(108, 192, 207, 1)', // #6CC0CF
		secondaryTopGradient: 'rgba(108, 192, 207, 0.8)',
		secondaryBottomGradient: 'rgba(108, 192, 207, 0.1)',
	},
	// 自定义双面积图默认配置
	custom: {
		// 第一个系列
		primary: 'rgba(32, 165, 10, 1)', // 主题绿色
		topGradient: 'rgba(32, 165, 10, 0.8)',
		bottomGradient: 'rgba(32, 165, 10, 0.1)',
		// 第二个系列
		secondary: 'rgba(255, 193, 7, 1)', // 黄色
		secondaryTopGradient: 'rgba(255, 193, 7, 0.8)',
		secondaryBottomGradient: 'rgba(255, 193, 7, 0.1)',
	},
};

/**
 * 创建图表数据配置的工厂函数（支持单系列和双系列）
 * @param {string} type - 图表类型 ('load', 'cpu', 'memory', 'network', 'diskio', 'custom')
 * @param {Array|Array[]} data - 图表数据，可选，默认使用通用数据。双系列时传入二维数组 [[data1], [data2]]
 * @param {Object} customConfig - 自定义配置，可选
 * @param {Object} seriesConfig - 系列配置，可选。格式：{ seriesNames: ['系列1', '系列2'], isDoubleArea: true }
 * @returns {Object} 响应式图表配置对象
 */
export const createChartData = (type, data = null, customConfig = {}, seriesConfig = {}) => {
	// 获取对应类型的颜色配置
	const colorConfig = chartColorConfig[type] || chartColorConfig.load;

	// 智能检测是否为双系列图表
	const isDoubleArea =
		seriesConfig.isDoubleArea ||
		(Array.isArray(data) && Array.isArray(data[0])) ||
		['network', 'diskio'].includes(type);

	if (isDoubleArea) {
		// 创建双系列图表
		return createDoubleSeriesChart(type, data, customConfig, seriesConfig, colorConfig);
	} else {
		// 创建单系列图表（保持向后兼容）
		return createSingleSeriesChart(type, data, customConfig, colorConfig);
	}
};

/**
 * 创建单系列图表的内部函数
 */
const createSingleSeriesChart = (type, data, customConfig, colorConfig) => {
	// 使用传入的数据或默认数据
	const chartData = data || commonChartConfig.defaultData;

	// 创建基础配置
	const baseConfig = {
		series: [
			{
				...commonChartConfig.series,
				data: chartData,
				lineStyle: {
					...commonChartConfig.series.lineStyle,
					color: colorConfig.primary,
				},
				areaStyle: {
					...commonChartConfig.series.areaStyle,
					color: {
						...commonChartConfig.series.areaStyle.color,
						colorStops: [
							{
								offset: 0,
								color: colorConfig.topGradient,
							},
							{
								offset: 1,
								color: colorConfig.bottomGradient,
							},
						],
					},
				},
				// 合并自定义series配置
				...customConfig.series,
			},
		],
		color: [colorConfig.primary],
		// 合并其他自定义配置
		...customConfig,
	};

	return reactive(baseConfig);
};

/**
 * 创建双系列图表的内部函数
 */
const createDoubleSeriesChart = (type, data, customConfig, seriesConfig, colorConfig) => {
	// 获取系列名称
	const defaultSeriesNames = getDefaultSeriesNames(type);
	const seriesNames = seriesConfig.seriesNames || defaultSeriesNames;

	// 处理数据
	let seriesData = [];
	if (Array.isArray(data) && Array.isArray(data[0])) {
		// 二维数组数据
		seriesData = data;
	} else if (Array.isArray(data)) {
		// 一维数组，复制为两个系列
		seriesData = [data, [...data]];
	} else {
		// 使用默认数据
		seriesData = [commonChartConfig.defaultData, [...commonChartConfig.defaultData]];
	}

	// 创建双系列配置
	const baseConfig = {
		categories: [], // x轴数据
		series: [
			// 第一个系列
			{
				name: seriesNames[0],
				...commonChartConfig.series,
				data: seriesData[0],
				lineStyle: {
					...commonChartConfig.series.lineStyle,
					color: colorConfig.primary,
				},
				areaStyle: {
					...commonChartConfig.series.areaStyle,
					color: {
						...commonChartConfig.series.areaStyle.color,
						colorStops: [
							{
								offset: 0,
								color: colorConfig.topGradient,
							},
							{
								offset: 1,
								color: colorConfig.bottomGradient,
							},
						],
					},
				},
			},
			// 第二个系列
			{
				name: seriesNames[1],
				...commonChartConfig.series,
				data: seriesData[1],
				lineStyle: {
					...commonChartConfig.series.lineStyle,
					color: colorConfig.secondary,
				},
				areaStyle: {
					...commonChartConfig.series.areaStyle,
					color: {
						...commonChartConfig.series.areaStyle.color,
						colorStops: [
							{
								offset: 0,
								color: colorConfig.secondaryTopGradient,
							},
							{
								offset: 1,
								color: colorConfig.secondaryBottomGradient,
							},
						],
					},
				},
			},
		],
		color: [colorConfig.primary, colorConfig.secondary],
		// 合并其他自定义配置
		...customConfig,
	};

	return reactive(baseConfig);
};

/**
 * 获取默认系列名称
 */
const getDefaultSeriesNames = (type) => {
	const defaultNames = {
		network: ['上传', '下载'],
		diskio: ['读取', '写入'],
		custom: ['系列1', '系列2'],
	};
	return defaultNames[type] || ['系列1', '系列2'];
};

// 重构后的负载图表样式配置函数
export const getLoadChartStyle = (loadData) => {
	return {
		...chartOpts,
		yAxis: {
			...chartOpts.yAxis,
			name: '负载',
			axisLine: { show: false },
			axisTick: { show: false },
			axisLabel: { show: false },
			splitLine: {
				show: true, // 保留横向辅助线
				lineStyle: {
					type: 'dashed', // 设置为虚线类型
					color: '#E7E7E7',
				},
			},
			splitNumber: 4, // 最多显示5条辅助线（4个分割区间）
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
		grid: { top: 10, right: 10, bottom: 10, left: 10, containLabel: false },
		xAxis: {
			show: false,
			type: 'category',
			boundaryGap: false, // 重要：让面积图从坐标轴开始
		},
	};
};

/**
 * 更新单个图表数据的辅助函数
 * @param {Object} chartData - 响应式图表数据对象
 * @param {Array} newData - 新的数据数组
 */
export const updateSingleChartData = (chartData, newData) => {
	if (chartData && chartData.series && chartData.series[0]) {
		chartData.series[0].data = [...newData];
	}
};

/**
 * 批量更新多个图表数据
 * @param {Object} updates - 更新配置对象 { chartData: newData, ... }
 */
export const batchUpdateChartData = (updates) => {
	Object.entries(updates).forEach(([chartData, newData]) => {
		updateSingleChartData(chartData, newData);
	});
};

/**
 * 获取图表当前数据
 * @param {Object} chartData - 响应式图表数据对象
 * @returns {Array} 当前数据数组
 */
export const getChartCurrentData = (chartData) => {
	return chartData?.series?.[0]?.data || [];
};

// 使用工厂函数创建图表数据实例
export const loadChartData = createChartData('load');
export const cpuChartData = createChartData('cpu');
export const memoryChartData = createChartData('memory');
export const diskChartData = createChartData('disk');
export const newNetworkChartData = createChartData('network', [
	[30, 10, 0, 20, 50, 60],
	[10, 20, 30, 10, 40, 10],
]);

// ==================== 页面数据配置 ====================
// 基础功能列表
export const basicFunctionList = ref([
	{
		label: '网站',
		image: '/static/index/web_site.png',
	},
	{
		label: '数据库',
		image: '/static/index/database.png',
	},
	{
		label: '系统防火墙',
		image: '/static/index/firewall.png',
	},
	{
		label: '终端',
		image: '/static/index/terminal.png',
	},
	{
		label: '安全风险',
		image: '/static/index/security_risk.png',
	},
	{
		label: '监控',
		image: '/static/index/system_monitor.png',
	},
	{
		label: 'SSH管理',
		image: '/static/index/ssh_manager.png',
	},
	{
		label: '文件管理',
		image: '/static/index/file_manager.png',
	},
	{
		label: '计划任务',
		image: '/static/index/cron_task.png',
	},
	{
		label: '日志',
		image: '/static/index/log_manager.png',
	},
]);

// 插件功能列表
export const pluginFunctionList = ref([
	{
		label: '站点监控',
		image: '/static/index/web_monitor.png',
	},
	{
		label: '系统加固',
		image: '/static/index/os_hardening.png',
	},
	{
		label: 'nginx防火墙',
		image: '/static/index/nginx_waf.png',
	},
	{
		label: '防篡改',
		image: '/static/index/tamper_protection.png',
	},
]);

// 环境功能列表
export const environmentFunctionList = ref([
	{
		label: 'nginx',
		image: '/static/index/nginx.png',
		imageStyle: 'width: 65rpx; height: 65rpx;',
	},
	{
		label: 'mysql',
		image: '/static/index/mysql.png',
		imageStyle: 'width: 60rpx; height: 60rpx;',
	},
	{
		label: 'redis',
		image: '/static/index/redis.png',
		imageStyle: 'width: 60rpx; height: 60rpx;',
	},
]);

// ==================== 背景图片和滚动效果 ====================
// 背景图片
export const backgroundImage = ref(bgLight);
export const backgroundImageStyle = ref({
	backgroundImage: `url(${backgroundImage.value})`,
	backgroundSize: 'cover',
	backgroundPosition: 'center',
});

// 背景图片缩放相关变量
export const serverBgScale = ref(1); // 初始缩放比例为1
export const serverBgTranslateY = ref(0); // 垂直偏移量
const maxScrollDistance = 200; // 最大滚动距离，超过此距离缩放效果不再变化
const minScale = 0.6; // 最小缩放比例
const maxTranslateY = -30; // 最大向上偏移量（负值表示向上）

// 计算背景图片的动态样式
export const serverBgStyle = computed(() => {
	return {
		transform: `scale(${serverBgScale.value}) translateY(${serverBgTranslateY.value}px)`,
		transition: 'transform 0.1s ease-out', // 添加平滑过渡效果
	};
});

// 页面滚动事件处理
export const onPageScrollHandler = (e) => {
	const scrollTop = e.detail.scrollTop;

	// 计算滚动比例
	const scrollRatio = Math.min(scrollTop / maxScrollDistance, 1);

	// 计算缩放比例：滚动距离越大，缩放比例越小
	const newScale = 1 - (1 - minScale) * scrollRatio;
	serverBgScale.value = Math.max(newScale, minScale);

	// 计算垂直偏移：滚动距离越大，向上偏移越多
	const newTranslateY = maxTranslateY * scrollRatio;
	serverBgTranslateY.value = newTranslateY;
};

// ==================== 使用示例和文档 ====================
/*
双面积图功能使用指南：

1. 基本使用（向后兼容）：
   // 单系列图表（现有方式不变）
   const cpuChart = createChartData('cpu');
   const memoryChart = createChartData('memory');

2. 双面积图创建：
   // 方式1: 直接使用 createChartData
   const networkChart = createChartData('network');
   const diskIOChart = createChartData('diskio');

   // 方式2: 使用便捷方法
   const networkChart2 = createNetworkDoubleAreaChart();
   const diskIOChart2 = createDiskIODoubleAreaChart();

   // 方式3: 自定义双面积图
   const customChart = createDoubleAreaChart('custom', {
     seriesNames: ['系列A', '系列B'],
     data: [[1, 2, 3, 4], [5, 6, 7, 8]],
     colors: ['#ff6b6b', '#4ecdc4']
   });

3. 高级配置：
   // 带自定义配置的双面积图
   const advancedChart = createChartData('network', null, {
     // ECharts 自定义配置
     grid: { top: 20, bottom: 20 }
   }, {
     seriesNames: ['上行流量', '下行流量'],
     isDoubleArea: true
   });

4. 数据更新：
   // 单系列更新
   updateSingleChartData(cpuChart, [10, 20, 30, 40]);

   // 双系列更新
   updateDoubleAreaChartData(networkChart, [
     [10, 20, 30], // 第一个系列
     [40, 50, 60]  // 第二个系列
   ]);

5. 智能检测：
   // 传入二维数组自动创建双系列
   const autoChart = createChartData('custom', [[1,2,3], [4,5,6]]);

   // 指定类型自动创建双系列
   const networkAuto = createChartData('network'); // 自动双系列

6. 颜色配置：
   支持的预设类型和颜色：
   - network: 橙色(#ff8c00) + 蓝色(#1e90ff)
   - diskio: 粉色(#FF4683) + 青色(#6CC0CF)
   - custom: 绿色(#20a50a) + 黄色(#ffc107)

注意事项：
- 保持向后兼容：现有单系列图表代码无需修改
- 双系列数据格式：[[series1Data], [series2Data]]
- 自动检测：network/diskio 类型自动创建双系列
- 响应式：所有图表数据都是响应式的，支持 Vue 3 响应式更新
*/
